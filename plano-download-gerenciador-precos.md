# Plano de Implementação - Download no Gerenciador de Preços

## Resumo da Funcionalidade
Implementar botão de download que permite baixar dados selecionados em CSV, com tratamento diferenciado para arquivos grandes (>50k registros).

## Critérios de Aceite
- ✅ Botão de Download visível na tela
- ✅ Botão desabilitado por padrão, habilitado apenas com seleções
- ✅ Download automático sem confirmação extra
- ✅ Formato do arquivo: `dd-mm-aaaa-ipa_gerenciador.csv`
- ✅ Conteúdo reflete exatamente as linhas selecionadas
- ✅ Para ≥50k registros: envio por email com modal informativo
- ✅ Template de email e link para download

## Análise Técnica

### Backend (Informações do Tech Lead)
- **< 50k linhas**: Retorna sequência de bytes (status 200)
- **≥ 50k linhas**: Retorna status 202 (processamento assíncrono)

### Estrutura Atual Identificada
1. **Componente Principal**: `GerenciadorPrecos.tsx`
2. **Tabela**: `GerenciadorTable.tsx` 
3. **Seleção**: Hook `useSelectedDatapoints.ts`
4. **Serviços**: Já existe `downloadDatapoints()` em `services`

## Plano de Implementação

### Fase 1: Análise Detalhada da Estrutura Atual
**Tempo estimado: 30 minutos**

1. **Investigar estrutura de seleção**
   - Analisar `useSelectedDatapoints.ts` em detalhes
   - Entender como `selectedIds` e `excludedIds` funcionam
   - Verificar estrutura de dados das linhas selecionadas

2. **Analisar serviço de download existente**
   - Examinar `downloadDatapoints()` atual
   - Verificar se já suporta filtros/seleções
   - Entender formato de resposta atual

3. **Identificar local ideal para o botão**
   - Analisar `GerenciadorTableHeading.tsx`
   - Verificar padrão de botões condicionais existentes

### Fase 2: Implementação do Botão de Download
**Tempo estimado: 45 minutos**

1. **Adicionar botão no GerenciadorTableHeading**
   ```tsx
   // Adicionar junto com botão "ALTERAR PREÇO"
   <ButtonPrimary
       id="download-dados"
       theme="ghost" 
       skin="blue"
       disabled={isEmptySelectedDatapoints}
       loading={isDownloadLoading}
       onClick={handleDownloadSelectedData}
   >
       DOWNLOAD
   </ButtonPrimary>
   ```

2. **Implementar lógica de habilitação**
   - Usar `isEmptySelectedDatapoints` existente
   - Adicionar estado de loading para download

### Fase 3: Implementação do Serviço de Download
**Tempo estimado: 60 minutos**

1. **Criar novo serviço `downloadSelectedDatapoints`**
   ```typescript
   // Em services/downloadDatapoints.ts
   export const downloadSelectedDatapoints = async (
       selectedIds: string[],
       excludedIds: string[],
       selectedAll: boolean,
       filters: any
   ) => {
       // Lógica para enviar IDs selecionados
       // Tratar resposta 200 vs 202
   }
   ```

2. **Implementar geração do nome do arquivo**
   ```typescript
   const generateFileName = () => {
       const today = new Date();
       const day = today.getDate().toString().padStart(2, '0');
       const month = (today.getMonth() + 1).toString().padStart(2, '0');
       const year = today.getFullYear();
       return `${day}-${month}-${year}-ipa_gerenciador.csv`;
   };
   ```

3. **Implementar download de blob (< 50k registros)**
   ```typescript
   const downloadBlob = (data: Blob, filename: string) => {
       const url = window.URL.createObjectURL(data);
       const link = document.createElement('a');
       link.href = url;
       link.download = filename;
       document.body.appendChild(link);
       link.click();
       document.body.removeChild(link);
       window.URL.revokeObjectURL(url);
   };
   ```

### Fase 4: Implementação do Modal para Arquivos Grandes
**Tempo estimado: 45 minutos**

1. **Criar componente ModalDownloadProcessing**
   ```tsx
   // Components/ModalDownloadProcessing/ModalDownloadProcessing.tsx
   interface Props {
       show: boolean;
       onClose: () => void;
   }
   
   const ModalDownloadProcessing = ({ show, onClose }: Props) => {
       return (
           <Modal show={show} onHide={onClose}>
               <Modal.Header>
                   <Modal.Title>Download em Processamento</Modal.Title>
               </Modal.Header>
               <Modal.Body>
                   Sua solicitação está sendo processada e será enviada por email, 
                   por favor, aguarde uns instantes.
               </Modal.Body>
               <Modal.Footer>
                   <Button onClick={onClose}>OK</Button>
               </Modal.Footer>
           </Modal>
       );
   };
   ```

2. **Integrar modal no componente principal**
   - Adicionar estado para controlar exibição
   - Mostrar quando resposta for 202

### Fase 5: Implementação da Lógica Principal
**Tempo estimado: 30 minutos**

1. **Criar handler principal no GerenciadorPrecos**
   ```typescript
   const handleDownloadSelectedData = async () => {
       try {
           const response = await downloadSelectedDatapoints(
               selectedDatapoints.selectedIds,
               selectedDatapoints.excludedIds, 
               selectedDatapoints.selectedAll,
               filtersModel
           );
           
           if (response.status === 200) {
               // Download direto
               const filename = generateFileName();
               downloadBlob(response.data, filename);
               Alert.success('Download realizado com sucesso');
           } else if (response.status === 202) {
               // Mostrar modal
               setShowDownloadModal(true);
           }
       } catch (error) {
           Alert.error('Erro ao realizar download');
       }
   };
   ```

### Fase 6: Ajustes e Melhorias
**Tempo estimado: 30 minutos**

1. **Adicionar loading states**
   - Usar `usePromiseTracker` existente
   - Área: 'download-selected-datapoints'

2. **Validações adicionais**
   - Verificar se há dados selecionados
   - Tratar casos de erro

3. **Testes de integração**
   - Testar com diferentes quantidades de seleção
   - Verificar formato do CSV gerado
   - Testar cenário de 50k+ registros

## Arquivos a Serem Modificados

### Principais
1. `src/pages/IPA/Lite/pages/GerenciadorPrecos/GerenciadorPrecos.tsx`
   - Adicionar handler de download
   - Adicionar estado do modal

2. `src/pages/IPA/RevisaoPrecos/Components/GerenciadorTableHeading/GerenciadorTableHeading.tsx`
   - Adicionar botão de download

3. `src/pages/IPA/Lite/lib/services/index.ts`
   - Implementar `downloadSelectedDatapoints`

### Novos Arquivos
1. `src/pages/IPA/Lite/components/ModalDownloadProcessing/`
   - Componente do modal
   - Estilos (se necessário)

## Considerações Técnicas

### Seleção de Dados
- Usar estrutura existente de `selectedDatapoints`
- Considerar `selectedAll` vs `selectedIds`/`excludedIds`
- Enviar filtros aplicados junto com seleção

### Performance
- Loading states durante download
- Não bloquear interface durante processamento
- Timeout adequado para requests grandes

### UX/UI
- Botão desabilitado quando nenhuma linha selecionada
- Feedback visual durante download
- Modal informativo para processamento assíncrono

### Backend Integration
- Endpoint deve receber IDs selecionados + filtros
- Tratar responses 200 (blob) vs 202 (async)
- Headers corretos para download de arquivo

## Riscos e Mitigações

### Riscos
1. **Performance**: Seleção de muitos registros pode ser lenta
2. **Memória**: Download de arquivos grandes pode consumir muita RAM
3. **Timeout**: Requests muito longos podem falhar

### Mitigações
1. Usar processamento assíncrono para arquivos grandes
2. Streaming de dados quando possível
3. Timeouts adequados e retry logic

## Estimativa Total
**Tempo total estimado: 4 horas**

## Próximos Passos
1. Validar plano com tech lead
2. Confirmar estrutura do endpoint backend
3. Definir template de email para arquivos grandes
4. Iniciar implementação seguindo as fases propostas
