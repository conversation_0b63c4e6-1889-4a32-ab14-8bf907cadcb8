# Plano de Implementação - Download no Gerenciador de Preços

## Resumo da Funcionalidade
Implementar botão de download que permite baixar dados selecionados em CSV. O botão fica habilitado apenas quando há itens selecionados na tabela.

## Critérios de Aceite
- ✅ Botão de Download visível na tela (ao lado dos botões "ALTERAR PREÇO" e "APLICAR PREÇO")
- ✅ Botão desabilitado por padrão, habilitado apenas com seleções
- ✅ Download automático sem confirmação extra
- ✅ Formato do arquivo: `dd-mm-aaaa-ipa_gerenciador.csv`
- ✅ Conteúdo reflete exatamente as linhas selecionadas
- ✅ Para ≥50k registros: processamento assíncrono com notificação por email
- ✅ Para <50k registros: download direto do arquivo

## Análise Técnica

### Backend (Informações do Tech Lead)
- **< 50k linhas**: Retorna sequência de bytes do arquivo CSV (status 200) - download direto
- **≥ 50k linhas**: Retorna status 202 - indica que o processamento será assíncrono e enviado por email

### Estrutura Atual Identificada
1. **Componente Principal**: `GerenciadorPrecos.tsx`
2. **Tabela**: `GerenciadorTable.tsx`
3. **Seleção**: Hook `useSelectedDatapoints.ts`
4. **Serviços**: Já existe `downloadDatapoints()` em `services`
5. **Interface**: Área de botões em `GerenciadorTableHeading.tsx`

## Plano de Implementação

### Fase 1: Análise Detalhada da Estrutura Atual
**Tempo estimado: 30 minutos**

1. **Investigar estrutura de seleção**
   - Analisar `useSelectedDatapoints.ts` em detalhes
   - Entender como `selectedIds` e `excludedIds` funcionam
   - Verificar estrutura de dados das linhas selecionadas

2. **Analisar serviço de download existente**
   - Examinar `downloadDatapoints()` atual
   - Verificar se já suporta filtros/seleções
   - Entender formato de resposta atual

3. **Identificar local ideal para o botão**
   - Analisar `GerenciadorTableHeading.tsx`
   - Verificar padrão de botões condicionais existentes

### Fase 2: Implementação do Botão de Download
**Tempo estimado: 30 minutos**

1. **Adicionar botão no GerenciadorTableHeading**
   ```tsx
   // Adicionar junto com botões "ALTERAR PREÇO" e "APLICAR PREÇO"
   <ButtonPrimary
       id="download-dados"
       theme="ghost"
       skin="blue"
       disabled={isEmptySelectedDatapoints}
       loading={isDownloadLoading}
       onClick={handleDownloadSelectedData}
   >
       DOWNLOAD
   </ButtonPrimary>
   ```

2. **Implementar lógica de habilitação**
   - Usar `isEmptySelectedDatapoints` existente
   - Adicionar estado de loading para download
   - Posicionar ao lado dos outros botões de ação

### Fase 3: Implementação do Serviço de Download
**Tempo estimado: 45 minutos**

1. **Criar novo serviço `downloadSelectedDatapoints`**
   ```typescript
   // Em services/downloadDatapoints.ts
   export const downloadSelectedDatapoints = async (
       selectedIds: string[],
       excludedIds: string[],
       selectedAll: boolean,
       filters: any
   ) => {
       // Enviar IDs selecionados para o backend
       // Retorno: 200 com bytes do CSV ou 202 para processamento assíncrono
   }
   ```

2. **Implementar geração do nome do arquivo**
   ```typescript
   const generateFileName = () => {
       const today = new Date();
       const day = today.getDate().toString().padStart(2, '0');
       const month = (today.getMonth() + 1).toString().padStart(2, '0');
       const year = today.getFullYear();
       return `${day}-${month}-${year}-ipa_gerenciador.csv`;
   };
   ```

3. **Implementar download direto (< 50k registros)**
   ```typescript
   const downloadFile = (data: Blob, filename: string) => {
       const url = window.URL.createObjectURL(data);
       const link = document.createElement('a');
       link.href = url;
       link.download = filename;
       document.body.appendChild(link);
       link.click();
       document.body.removeChild(link);
       window.URL.revokeObjectURL(url);
   };
   ```

### Fase 4: Implementação da Notificação para Arquivos Grandes
**Tempo estimado: 15 minutos**

1. **Implementar notificação simples**
   ```typescript
   // Quando resposta for 202, mostrar alert informativo
   if (response.status === 202) {
       Alert.info(
           'Sua solicitação está sendo processada e será enviada por email, por favor, aguarde uns instantes.',
           5000 // 5 segundos
       );
   }
   ```

2. **Sem necessidade de modal**
   - Usar Alert existente do rsuite
   - Mensagem informativa simples
   - Usuário continua usando a aplicação normalmente

### Fase 5: Implementação da Lógica Principal
**Tempo estimado: 30 minutos**

1. **Criar handler principal no GerenciadorPrecos**
   ```typescript
   const handleDownloadSelectedData = async () => {
       try {
           const response = await downloadSelectedDatapoints(
               selectedDatapoints.selectedIds,
               selectedDatapoints.excludedIds,
               selectedDatapoints.selectedAll,
               filtersModel
           );

           if (response.status === 200) {
               // Download direto - arquivo pequeno
               const filename = generateFileName();
               downloadFile(response.data, filename);
               Alert.success('Download realizado com sucesso');
           } else if (response.status === 202) {
               // Processamento assíncrono - arquivo grande
               Alert.info(
                   'Sua solicitação está sendo processada e será enviada por email, por favor, aguarde uns instantes.',
                   5000
               );
           }
       } catch (error) {
           Alert.error('Erro ao realizar download');
       }
   };
   ```

### Fase 6: Ajustes e Melhorias
**Tempo estimado: 20 minutos**

1. **Adicionar loading states**
   - Usar `usePromiseTracker` existente
   - Área: 'download-selected-datapoints'

2. **Validações adicionais**
   - Verificar se há dados selecionados
   - Tratar casos de erro

3. **Testes básicos**
   - Testar habilitação/desabilitação do botão
   - Verificar download com poucos registros
   - Testar notificação para muitos registros

## Arquivos a Serem Modificados

### Principais
1. `src/pages/IPA/Lite/pages/GerenciadorPrecos/GerenciadorPrecos.tsx`
   - Adicionar handler de download
   - Adicionar estado do modal

2. `src/pages/IPA/RevisaoPrecos/Components/GerenciadorTableHeading/GerenciadorTableHeading.tsx`
   - Adicionar botão de download

3. `src/pages/IPA/Lite/lib/services/index.ts`
   - Implementar `downloadSelectedDatapoints`

### Novos Arquivos
1. Nenhum arquivo novo necessário - usar componentes existentes

## Considerações Técnicas

### Seleção de Dados
- Usar estrutura existente de `selectedDatapoints`
- Considerar `selectedAll` vs `selectedIds`/`excludedIds`
- Enviar filtros aplicados junto com seleção

### Performance
- Loading states durante download
- Não bloquear interface durante processamento
- Timeout adequado para requests grandes

### UX/UI
- Botão desabilitado quando nenhuma linha selecionada
- Feedback visual durante download
- Modal informativo para processamento assíncrono

### Backend Integration
- Endpoint deve receber IDs selecionados + filtros
- Tratar responses 200 (blob) vs 202 (async)
- Headers corretos para download de arquivo

## Riscos e Mitigações

### Riscos
1. **Performance**: Seleção de muitos registros pode ser lenta
2. **Memória**: Download de arquivos grandes pode consumir muita RAM
3. **Timeout**: Requests muito longos podem falhar

### Mitigações
1. Usar processamento assíncrono para arquivos grandes
2. Streaming de dados quando possível
3. Timeouts adequados e retry logic

## Estimativa Total
**Tempo total estimado: 4 horas**

## Próximos Passos
1. Validar plano com tech lead
2. Confirmar estrutura do endpoint backend
3. Definir template de email para arquivos grandes
4. Iniciar implementação seguindo as fases propostas
